<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Panel</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
  <div class="container mx-auto p-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Agent Management</h1>
      <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded">
        Logout
      </button>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow mb-6">
      <h2 class="text-xl font-semibold mb-4">Create New Agent</h2>
      <form id="agentForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block mb-1">Name</label>
          <input type="text" id="name" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block mb-1">Email</label>
          <input   id="email" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block mb-1">Phone</label>
          <input type="tel" id="phone" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block mb-1">Password</label>
          <input type="password" id="password" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block mb-1">PIN Code</label>
          <input type="text" id="pincode" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block mb-1">Location</label>
          <input type="text" id="location" required class="w-full p-2 border rounded">
        </div>
        <div class="md:col-span-2">
          <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
            Create Agent
          </button>
        </div>
      </form>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">All Agents</h2>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead>
            <tr class="border-b">
              <th class="py-2 px-4 text-left">Name</th>
              <th class="py-2 px-4 text-left">Email</th>
              <th class="py-2 px-4 text-left">Phone</th>
              <th class="py-2 px-4 text-left">Location</th>
              <th class="py-2 px-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody id="agentsTable"></tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Check if admin is logged in
      if (localStorage.getItem('userType') !== 'admin') {
        window.location.href = '/login';
      }
      
      fetchAgents();
      
      document.getElementById('agentForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const agent = {
          name: document.getElementById('name').value,
          email: document.getElementById('email').value,
          phone: document.getElementById('phone').value,
          password: document.getElementById('password').value,
          pincode: document.getElementById('pincode').value,
          location: document.getElementById('location').value
        };
        
        try {
          const response = await fetch('http://localhost:3000/api/leads', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(agent)
          });
          
          if (response.ok) {
            alert('Agent created successfully!');
            document.getElementById('agentForm').reset();
            fetchAgents();
          } else {
            alert('Failed to create agent');
          }
        } catch (err) {
          alert('Error creating agent');
        }
      });
    });
    
    async function fetchAgents() {
      try {
        const response = await fetch('http://localhost:3000/api/leads');
        const agents = await response.json();
        
        document.getElementById('agentsTable').innerHTML = agents.map(agent => `
          <tr class="border-b hover:bg-gray-50">
            <td class="py-2 px-4">${agent.name}</td>
            <td class="py-2 px-4">${agent.email}</td>
            <td class="py-2 px-4">${agent.phone}</td>
            <td class="py-2 px-4">${agent.location}</td>
            <td class="py-2 px-4 flex gap-2">
            
              <button onclick="sendEmail('${agent._id}', 'welcome')" class="text-green-500">
                <i class="fas fa-envelope"></i>
              </button>
            <button onclick="window.location.href='/agentlog.html?id=${agent.phone}'" class="text-blue-500">
        <i class="fas fa-eye"></i>
      </button>
            </td>
          </tr>
        `).join('');
      } catch (err) {
        console.error('Error fetching agents:', err);
      }
    }
    
    function viewAgent(id) {
      window.location.href = `agent.html?id=${id}`;
    }
    
    async function sendEmail(agentId, emailType) {
      try {
        const response = await fetch('http://localhost:3000/api/send-email', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ agentId, emailType })
        });
        
        if (response.ok) {
          alert(`${emailType} email sent successfully!`);
        } else {
          alert('Failed to send email');
        }
      } catch (err) {
        alert('Error sending email');
      }
    }
    
    function logout() {
      localStorage.clear();
      window.location.href = '/login';
    }
  </script>
</body>
</html>