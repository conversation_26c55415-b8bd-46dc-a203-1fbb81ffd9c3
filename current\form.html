<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Volmo Logistics - Franchisee Application</title>
    <link
      rel="shortcut icon"
      href="https://play-lh.googleusercontent.com/5NoxwjDfgEN36Jjzi-VdwT_xfNgLylDom4nWx7bv60s7yC4e-pgkl32_vbwjaBHS-A"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
    />
    <style>
      .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 8px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(150%);
        transition: transform 0.3s ease-in-out;
        z-index: 1000;
      }
      .toast.show {
        transform: translateX(0);
      }
      .toast.success {
        background-color: #10b981;
      }
      .toast.error {
        background-color: #ef4444;
      }
      .file-upload {
        border: 2px dashed #cbd5e0;
        transition: all 0.3s ease;
      }
      .file-upload:hover {
        border-color: #4f46e5;
        background-color: #f8fafc;
      }
      .file-upload.dragover {
        border-color: #4f46e5;
        background-color: #e0e7ff;
      }
    </style>
  </head>
  <body class="bg-gray-50 font-sans">
    <!-- Toast Notification -->
    <div id="toast" class="toast"></div>

    <!-- Header -->
    <header class="bg-indigo-700 text-white py-6 shadow-lg">
      <div class="container mx-auto px-4 flex justify-between items-center">
        <div class="flex items-center space-x-3">
          <i class="fas fa-truck-fast text-3xl"></i>
          <h1 class="text-2xl font-bold">Volmo Logistics</h1>
        </div>
        <div class="text-sm">
          Application No:
          <span id="applicationNumberDisplay" class="font-mono">VL-XXXXXX</span>
        </div>
      </div>
    </header>

    <main class="container mx-auto px-4 py-8">
      <form
        id="franchiseForm"
        class="bg-white rounded-lg shadow-md overflow-hidden p-6"
      >
        <!-- Section 1: Personal Information -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-user-circle mr-2 text-indigo-600"></i> Personal
          Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label
              for="fullName"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Full Name*</label
            >
            <input
              type="text"
              id="fullName"
              name="fullName"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="fatherHusbandName"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Father's/Husband's Name*</label
            >
            <input
              type="text"
              id="fatherHusbandName"
              name="fatherHusbandName"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="dateOfBirth"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Date of Birth*</label
            >
            <input
              type="date"
              id="dateOfBirth"
              name="dateOfBirth"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Gender*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="gender"
                  value="Male"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Male</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="gender"
                  value="Female"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Female</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="gender"
                  value="Other"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Other</span></label
              >
            </div>
          </div>
          <div>
            <label
              for="nationality"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Nationality*</label
            >
            <input
              type="text"
              id="nationality"
              name="nationality"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Marital Status*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="maritalStatus"
                  value="Single"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Single</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="maritalStatus"
                  value="Married"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Married</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="maritalStatus"
                  value="Other"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Other</span></label
              >
            </div>
          </div>
          <div>
            <label
              for="panNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >PAN Number*</label
            >
            <input
              type="text"
              id="panNumber"
              name="panNumber"
              required
              pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
              title="Enter valid PAN (e.g., **********)"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 uppercase"
            />
          </div>
          <div>
            <label
              for="aadharNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Aadhar Number*</label
            >
            <input
              type="text"
              id="aadharNumber"
              name="aadharNumber"
              required
              pattern="[0-9]{12}"
              title="Enter 12-digit Aadhar number"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="passportNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Passport Number (if available)</label
            >
            <input
              type="text"
              id="passportNumber"
              name="passportNumber"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- Section 2: Contact Details -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-address-book mr-2 text-indigo-600"></i> Contact
          Details
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label
              for="mobileNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Mobile Number*</label
            >
            <input
              type="tel"
              id="mobileNumber"
              name="mobileNumber"
              required
              pattern="[0-9]{10}"
              title="Enter 10-digit mobile number"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="alternateMobileNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Alternate Mobile Number</label
            >
            <input
              type="tel"
              id="alternateMobileNumber"
              name="alternateMobileNumber"
              pattern="[0-9]{10}"
              title="Enter 10-digit mobile number"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="email"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Email ID*</label
            >
            <input
              type="email"
              id="email"
              name="email"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Preferred Mode of Communication*</label
            >
            <div class="flex flex-col space-y-2">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="preferredCommunication"
                  value="Phone"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Phone</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="preferredCommunication"
                  value="Email"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Email</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="preferredCommunication"
                  value="WhatsApp"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">WhatsApp</span></label
              >
            </div>
          </div>
        </div>

        <!-- Section 3: Residential Address -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-home mr-2 text-indigo-600"></i> Residential Address
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div class="md:col-span-2">
            <label
              for="residentialStreet"
              class="block text-sm font-medium text-gray-700 mb-1"
              >House No./Street*</label
            >
            <input
              type="text"
              id="residentialStreet"
              name="residentialStreet"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
         

<div>
  <label for="residentialCity" class="block text-sm font-medium text-gray-700 mb-1">City*</label>
  <input
    type="text"
    id="residentialCity"
    name="residentialCity"
    required
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>
<div>
  <label for="residentialDistrict" class="block text-sm font-medium text-gray-700 mb-1">District*</label>
  <input
    type="text"
    id="residentialDistrict"
    name="residentialDistrict"
    required
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>
<div>
  <label for="residentialState" class="block text-sm font-medium text-gray-700 mb-1">State*</label>
  <input
    type="text"
    id="residentialState"
    name="residentialState"
    required
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>
<div style="width: 100%; max-width: 400px; margin: 0 auto; font-family: Arial, sans-serif;">
  <label
    for="residentialPinCode"
    style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;"
  >
    PIN Code*
  </label>
  <input
    type="text"
    id="residentialPinCode"
    name="residentialPinCode"
    required
    pattern="[0-9]{6}"
    title="Enter 6-digit PIN code"
    onchange="fetchResidentialPincodeDetails(this.value)"
    style="width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 16px; box-sizing: border-box; outline: none;"
    onfocus="this.style.border='1px solid #6366f1';"
    onblur="this.style.border='1px solid #d1d5db';"
  />
</div>

<!-- Result Display -->
<div id="pincodeResult" style="margin-top: 8px; font-size: 14px; color: #374151"></div>

<script>
function fetchResidentialPincodeDetails(pincode) {
  if (!/^[0-9]{6}$/.test(pincode)) {
    document.getElementById("pincodeResult").innerHTML = `<span style="color:#ef4444;">Invalid PIN code</span>`;
    return;
  }

  fetch(`https://api.postalpincode.in/pincode/${pincode}`)
    .then((res) => res.json())
    .then((data) => {
      if (data[0].Status === "Success") {
        let postOffice = data[0].PostOffice[0];

        // Auto-fill residential address fields
        document.getElementById("residentialCity").value = postOffice.Block || postOffice.Name || "";
        document.getElementById("residentialDistrict").value = postOffice.District || "";
        document.getElementById("residentialState").value = postOffice.State || "";

        document.getElementById("pincodeResult").innerHTML = `<span style="color:green;">Details filled successfully</span>`;
      } else {
        document.getElementById("pincodeResult").innerHTML = `<span style="color:#ef4444;">No details found</span>`;
      }
    })
    .catch(() => {
      document.getElementById("pincodeResult").innerHTML = `<span style="color:#ef4444;">Error fetching details</span>`;
    });
}
</script>

<!-- Mobile-friendly handling -->
<style>
  @media (max-width: 480px) {
    #residentialPinCode {
      font-size: 14px;
      padding: 8px;
    }
    label {
      font-size: 13px !important;
    }
    #pincodeResult {
      font-size: 13px !important;
    }
  }
</style>








        </div>

        <!-- Section 4: Business Information -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-briefcase mr-2 text-indigo-600"></i> Business
          Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label
              for="businessName"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Business Name (if applicable)</label
            >
            <input
              type="text"
              id="businessName"
              name="businessName"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Type of Business*</label
            >
            <div class="grid grid-cols-2 gap-2">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="businessType"
                  value="Sole Proprietor"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Sole Proprietor</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="businessType"
                  value="Partnership"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Partnership</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="businessType"
                  value="Private Ltd"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Private Ltd</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="businessType"
                  value="LLP"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">LLP</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="businessType"
                  value="Other"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Other</span></label
              >
            </div>
          </div>
          <div>
            <label
              for="gstNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >GST Number (if applicable)</label
            >
            <input
              type="text"
              id="gstNumber"
              name="gstNumber"
              pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}"
              title="Enter valid GST number"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 uppercase"
            />
          </div>
          <div class="md:col-span-2">
            <label
              for="officeAddress"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Office Address*</label
            >
            <input
              type="text"
              id="officeAddress"
              name="officeAddress"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>


          
<div>
  <label for="officeCity" class="block text-sm font-medium text-gray-700 mb-1">City*</label>
  <input
    type="text"
    id="officeCity"
    name="officeCity"
    required
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>
<div>
  <label for="officeDistrict" class="block text-sm font-medium text-gray-700 mb-1">District*</label>
  <input
    type="text"
    id="officeDistrict"
    name="officeDistrict"
    required
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>
<div>
  <label for="officeState" class="block text-sm font-medium text-gray-700 mb-1">State*</label>
  <input
    type="text"
    id="officeState"
    name="officeState"
    required
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>
<div>
  <label for="officePinCode" class="block text-sm font-medium text-gray-700 mb-1">PIN Code*</label>
  <input
    type="text"
    onchange="fetchOfficePincodeDetails(this.value)"
    id="officePinCode"
    name="officePinCode"
    required
    pattern="[0-9]{6}"
    title="Enter 6-digit PIN code"
    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
  />
</div>

<!-- Result Display -->
<div id="pincodeResults" style="margin-top: 8px; font-size: 14px; color: #374151"></div>

<script>
function fetchOfficePincodeDetails(pincode) {
  if (!/^[0-9]{6}$/.test(pincode)) {
    document.getElementById("pincodeResults").innerHTML = `<span style="color:#ef4444;">Invalid PIN code</span>`;
    return;
  }

  fetch(`https://api.postalpincode.in/pincode/${pincode}`)
    .then(res => res.json())
    .then(data => {
      if (data[0].Status === "Success") {
        let postOffice = data[0].PostOffice[0];

        // Auto-fill fields
        document.getElementById("officeCity").value = postOffice.Block || postOffice.Name || "";
        document.getElementById("officeDistrict").value = postOffice.District || "";
        document.getElementById("officeState").value = postOffice.State || "";

        document.getElementById("pincodeResults").innerHTML = `<span style="color:green;">Details filled successfully</span>`;
      } else {
        document.getElementById("pincodeResults").innerHTML = `<span style="color:#ef4444;">No details found</span>`;
      }
    })
    .catch(() => {
      document.getElementById("pincodeResults").innerHTML = `<span style="color:#ef4444;">Error fetching details</span>`;
    });
}
</script>

          <div>
            <label
              for="numberOfEmployees"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Number of Employees in Your Business (if applicable)</label
            >
            <input
              type="number"
              id="numberOfEmployees"
              name="numberOfEmployees"
              min="0"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- Section 5: Franchise Location Details -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-map-marker-alt mr-2 text-indigo-600"></i> Franchise
          Location Details
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label
              for="franchisePinCode"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Preferred PIN Code for Franchise*</label
            >
            <input
              type="text"
              id="franchisePinCode"
              name="franchisePinCode"
              required
              pattern="[0-9]{6}"
              title="Enter 6-digit PIN code"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Do you own or rent the business premises?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="premisesOwnership"
                  value="Own"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Own</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="premisesOwnership"
                  value="Rent"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Rent</span></label
              >
            </div>
          </div>
          <div>
            <label
              for="totalSpace"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Total Available Space for Franchise (sq. ft.)*</label
            >
            <input
              type="number"
              id="totalSpace"
              name="totalSpace"
              required
              min="100"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="warehouseSpace"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Warehouse Space (if any) (sq. ft.)</label
            >
            <input
              type="number"
              id="warehouseSpace"
              name="warehouseSpace"
              min="0"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Parking Facility Available?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="parkingFacility"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="parkingFacility"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Office Setup Availability*</label
            >
            <div class="flex flex-col space-y-2">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="officeSetup"
                  value="Fully Furnished"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Fully Furnished</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="officeSetup"
                  value="Partially Furnished"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700"
                  >Partially Furnished</span
                ></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="officeSetup"
                  value="Unfurnished"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Unfurnished</span></label
              >
            </div>
          </div>
        </div>

        <!-- Section 6: Investment & Financial Information -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-rupee-sign mr-2 text-indigo-600"></i> Investment &
          Financial Information
        </h2>
        <div class="bg-indigo-50 p-6 rounded-lg mb-6">
          <h3 class="text-lg font-semibold text-indigo-800 mb-4">
            Registration & Fees
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="text-sm text-gray-600">Registration Fee</p>
              <p class="text-lg font-bold">₹18,600</p>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="text-sm text-gray-600">Security Money</p>
              <p class="text-lg font-bold">
                90% refundable after the agreement
              </p>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="text-sm text-gray-600">
                Interest Earned on Security Deposit
              </p>
              <p class="text-lg font-bold">7.5% annually</p>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="text-sm text-gray-600">One-time Setup Fee</p>
              <p class="text-lg font-bold">₹2,00,000 (lifetime investment)</p>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="text-sm text-gray-600">Agreement Fee</p>
              <p class="text-lg font-bold">₹90,100 (fully refundable)</p>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Investment Capacity*</label
            >
            <div class="flex flex-col space-y-2">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentCapacity"
                  value="Below ₹5 Lakhs"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Below ₹5 Lakhs</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentCapacity"
                  value="₹5-10 Lakhs"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">₹5-10 Lakhs</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentCapacity"
                  value="₹10-20 Lakhs"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">₹10-20 Lakhs</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentCapacity"
                  value="Above ₹20 Lakhs"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Above ₹20 Lakhs</span></label
              >
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Source of Investment*</label
            >
            <div class="flex flex-col space-y-2">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentSource"
                  value="Self-Funded"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Self-Funded</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentSource"
                  value="Loan"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Loan</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentSource"
                  value="Business Partner"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700"
                  >Business Partner</span
                ></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="investmentSource"
                  value="Other"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Other</span></label
              >
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Do you have existing loans or liabilities?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasLoans"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasLoans"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                  checked
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div id="loanDetailsContainer" class="hidden">
            <label
              for="loanDetails"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Loan/Liability Details</label
            >
            <textarea
              id="loanDetails"
              name="loanDetails"
              rows="3"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div>
            <label
              for="expectedRevenue"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Expected Monthly Revenue from Franchise (Approx.)*</label
            >
            <div class="relative rounded-md shadow-sm">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <span class="text-gray-500 sm:text-sm">₹</span>
              </div>
              <input
                type="number"
                id="expectedRevenue"
                name="expectedRevenue"
                required
                min="0"
                class="block w-full pl-8 pr-12 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <div
                class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
              >
                <span class="text-gray-500 sm:text-sm">.00</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Section 7: Logistics & Operational Readiness -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-truck mr-2 text-indigo-600"></i> Logistics &
          Operational Readiness
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Do you own commercial vehicles for logistics?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasCommercialVehicles"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasCommercialVehicles"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                  checked
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div id="vehicleDetailsContainer" class="hidden">
            <label
              for="vehicleDetails"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Vehicle Details</label
            >
            <textarea
              id="vehicleDetails"
              name="vehicleDetails"
              rows="3"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Are you familiar with logistics and transportation
              operations?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="isFamiliarWithLogistics"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="isFamiliarWithLogistics"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Do you have experience with courier/logistics services?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasLogisticsExperience"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasLogisticsExperience"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                  checked
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div id="experienceDetailsContainer" class="hidden md:col-span-2">
            <label
              for="experienceDetails"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Company Details</label
            >
            <textarea
              id="experienceDetails"
              name="experienceDetails"
              rows="3"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div>
            <label
              for="staffCount"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Number of Staff You Can Employ for the Franchise*</label
            >
            <input
              type="number"
              id="staffCount"
              name="staffCount"
              required
              min="1"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- Section 8: Qualification Details -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-graduation-cap mr-2 text-indigo-600"></i>
          Qualification Details
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Highest Educational Qualification*</label
            >
            <div class="flex flex-col space-y-2">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="education"
                  value="10th Pass"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">10th Pass</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="education"
                  value="12th Pass"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">12th Pass</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="education"
                  value="Diploma"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Diploma</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="education"
                  value="Graduate"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Graduate</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="education"
                  value="Postgraduate"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Postgraduate</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="education"
                  value="Other"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Other</span></label
              >
            </div>
          </div>
          <div>
            <label
              for="professionalBackground"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Professional Background (if any)</label
            >
            <textarea
              id="professionalBackground"
              name="professionalBackground"
              rows="4"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div class="md:col-span-2">
            <label
              for="certifications"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Relevant Certifications (if any)</label
            >
            <textarea
              id="certifications"
              name="certifications"
              rows="3"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
        </div>

        <!-- Section 9: Past Business Experience & References -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-history mr-2 text-indigo-600"></i> Past Business
          Experience & References
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Do you currently own any other franchise?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasOtherFranchise"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasOtherFranchise"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                  checked
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div id="franchiseDetailsContainer" class="hidden">
            <label
              for="franchiseDetails"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Company Name</label
            >
            <input
              type="text"
              id="franchiseDetails"
              name="franchiseDetails"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Have you been involved in any business disputes or legal
              issues?*</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasLegalIssues"
                  value="Yes"
                  required
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                /><span class="ml-2 text-gray-700">Yes</span></label
              >
              <label class="inline-flex items-center"
                ><input
                  type="radio"
                  name="hasLegalIssues"
                  value="No"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                  checked
                /><span class="ml-2 text-gray-700">No</span></label
              >
            </div>
          </div>
          <div id="legalDetailsContainer" class="hidden">
            <label
              for="legalDetails"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Details</label
            >
            <textarea
              id="legalDetails"
              name="legalDetails"
              rows="3"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
        </div>
        <h3 class="text-lg font-semibold text-gray-800 mt-8 mb-4">
          Business References
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-700 mb-3">Reference 1</h4>
            <div class="space-y-4">
              <div>
                <label
                  for="reference1Name"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Name*</label
                ><input
                  type="text"
                  id="reference1Name"
                  name="reference1Name"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label
                  for="reference1Contact"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Contact No.*</label
                ><input
                  type="tel"
                  id="reference1Contact"
                  name="reference1Contact"
                  required
                  pattern="[0-9]{10}"
                  title="Enter 10-digit contact number"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label
                  for="reference1Relationship"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Relationship*</label
                ><input
                  type="text"
                  id="reference1Relationship"
                  name="reference1Relationship"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
            </div>
          </div>
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-700 mb-3">Reference 2</h4>
            <div class="space-y-4">
              <div>
                <label
                  for="reference2Name"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Name*</label
                ><input
                  type="text"
                  id="reference2Name"
                  name="reference2Name"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label
                  for="reference2Contact"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Contact No.*</label
                ><input
                  type="tel"
                  id="reference2Contact"
                  name="reference2Contact"
                  required
                  pattern="[0-9]{10}"
                  title="Enter 10-digit contact number"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label
                  for="reference2Relationship"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Relationship*</label
                ><input
                  type="text"
                  id="reference2Relationship"
                  name="reference2Relationship"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Section 10: Bank Details -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-university mr-2 text-indigo-600"></i> Bank Details
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div>
            <label
              for="bankName"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Bank Name*</label
            ><input
              type="text"
              id="bankName"
              name="bankName"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="bankBranch"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Branch*</label
            ><input
              type="text"
              id="bankBranch"
              name="bankBranch"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="accountHolderName"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Account Holder's Name*</label
            ><input
              type="text"
              id="accountHolderName"
              name="accountHolderName"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="accountNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Account Number*</label
            ><input
              type="text"
              id="accountNumber"
              name="accountNumber"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <div>
            <label
              for="ifscCode"
              class="block text-sm font-medium text-gray-700 mb-1"
              >IFSC Code*</label
            ><input
              type="text"
              id="ifscCode"
              name="ifscCode"
              required
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 uppercase"
            />
          </div>
          <div>
            <label
              for="upiId"
              class="block text-sm font-medium text-gray-700 mb-1"
              >UPI ID (if any)</label
            ><input
              type="text"
              id="upiId"
              name="upiId"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- Section 11: Document Upload -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-file-upload mr-2 text-indigo-600"></i> Document
          Upload
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div class="file-upload-container">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >PAN Card*</label
            >
            <div
              id="panCardUpload"
              class="file-upload relative p-6 text-center cursor-pointer"
            >
              <input
                type="file"
                id="panCard"
                name="panCard"
                accept="image/*,.pdf"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                required
              /><i
                class="fas fa-cloud-upload-alt text-4xl text-indigo-500 mb-2"
              ></i>
              <p class="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 5MB</p>
              <div id="panCardPreview" class="mt-4 hidden">
                <div
                  class="flex items-center justify-between bg-gray-100 p-2 rounded"
                >
                  <div class="flex items-center">
                    <i class="fas fa-file text-indigo-500 mr-2"></i
                    ><span
                      id="panCardFileName"
                      class="text-sm text-gray-700 truncate"
                    ></span>
                  </div>
                  <button
                    type="button"
                    onclick="removeFile('panCard')"
                    class="text-red-500 hover:text-red-700"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="file-upload-container">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Aadhar Card Frontend*</label
            >
            <div
              id="aadharCardUpload"
              class="file-upload relative p-6 text-center cursor-pointer"
            >
              <input
                type="file"
                id="aadharCard"
                name="aadharCard"
                accept="image/*,.pdf"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                required
              /><i
                class="fas fa-cloud-upload-alt text-4xl text-indigo-500 mb-2"
              ></i>
              <p class="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 5MB</p>
              <div id="aadharCardPreview" class="mt-4 hidden">
                <div
                  class="flex items-center justify-between bg-gray-100 p-2 rounded"
                >
                  <div class="flex items-center">
                    <i class="fas fa-file text-indigo-500 mr-2"></i
                    ><span
                      id="aadharCardFileName"
                      class="text-sm text-gray-700 truncate"
                    ></span>
                  </div>
                  <button
                    type="button"
                    onclick="removeFile('aadharCard')"
                    class="text-red-500 hover:text-red-700"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="file-upload-container">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Adhar Card Backside (if available)</label
            >
            <div
              id="passportUpload"
              class="file-upload relative p-6 text-center cursor-pointer"
            >
              <input
                type="file"
                id="passport"
                name="passport"
                accept="image/*,.pdf"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              /><i
                class="fas fa-cloud-upload-alt text-4xl text-indigo-500 mb-2"
              ></i>
              <p class="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 5MB</p>
              <div id="passportPreview" class="mt-4 hidden">
                <div
                  class="flex items-center justify-between bg-gray-100 p-2 rounded"
                >
                  <div class="flex items-center">
                    <i class="fas fa-file text-indigo-500 mr-2"></i
                    ><span
                      id="passportFileName"
                      class="text-sm text-gray-700 truncate"
                    ></span>
                  </div>
                  <button
                    type="button"
                    onclick="removeFile('passport')"
                    class="text-red-500 hover:text-red-700"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="file-upload-container">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >GST Certificate (if applicable)</label
            >
            <div
              id="gstCertificateUpload"
              class="file-upload relative p-6 text-center cursor-pointer"
            >
              <input
                type="file"
                id="gstCertificate"
                name="gstCertificate"
                accept="image/*,.pdf"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              /><i
                class="fas fa-cloud-upload-alt text-4xl text-indigo-500 mb-2"
              ></i>
              <p class="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 5MB</p>
              <div id="gstCertificatePreview" class="mt-4 hidden">
                <div
                  class="flex items-center justify-between bg-gray-100 p-2 rounded"
                >
                  <div class="flex items-center">
                    <i class="fas fa-file text-indigo-500 mr-2"></i
                    ><span
                      id="gstCertificateFileName"
                      class="text-sm text-gray-700 truncate"
                    ></span>
                  </div>
                  <button
                    type="button"
                    onclick="removeFile('gstCertificate')"
                    class="text-red-500 hover:text-red-700"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="file-upload-container">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Passport side photo (if applicable)</label
            >
            <div
              id="businessProofUpload"
              class="file-upload relative p-6 text-center cursor-pointer"
            >
              <input
                type="file"
                id="businessProof"
                name="businessProof"
                accept="image/*,.pdf"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              /><i
                class="fas fa-cloud-upload-alt text-4xl text-indigo-500 mb-2"
              ></i>
              <p class="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 5MB</p>
              <div id="businessProofPreview" class="mt-4 hidden">
                <div
                  class="flex items-center justify-between bg-gray-100 p-2 rounded"
                >
                  <div class="flex items-center">
                    <i class="fas fa-file text-indigo-500 mr-2"></i
                    ><span
                      id="businessProofFileName"
                      class="text-sm text-gray-700 truncate"
                    ></span>
                  </div>
                  <button
                    type="button"
                    onclick="removeFile('businessProof')"
                    class="text-red-500 hover:text-red-700"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="file-upload-container">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Bank Document*</label
            >
            <div
              id="cancelledChequeUpload"
              class="file-upload relative p-6 text-center cursor-pointer"
            >
              <input
                type="file"
                id="cancelledCheque"
                name="cancelledCheque"
                accept="image/*,.pdf"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                required
              /><i
                class="fas fa-cloud-upload-alt text-4xl text-indigo-500 mb-2"
              ></i>
              <p class="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 5MB</p>
              <div id="cancelledChequePreview" class="mt-4 hidden">
                <div
                  class="flex items-center justify-between bg-gray-100 p-2 rounded"
                >
                  <div class="flex items-center">
                    <i class="fas fa-file text-indigo-500 mr-2"></i
                    ><span
                      id="cancelledChequeFileName"
                      class="text-sm text-gray-700 truncate"
                    ></span>
                  </div>
                  <button
                    type="button"
                    onclick="removeFile('cancelledCheque')"
                    class="text-red-500 hover:text-red-700"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Section 12: Review & Submit Content -->
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
          <i class="fas fa-check-circle mr-2 text-indigo-600"></i> Final Review
          & Declaration
        </h2>
        <div class="bg-gray-50 p-6 rounded-lg mb-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">
            Terms & Conditions
          </h3>
          <div
            class="bg-white p-4 rounded-lg shadow-sm max-h-64 overflow-y-auto"
          >
            <ol class="list-decimal pl-5 space-y-3 text-sm text-gray-700">
              <li>
                The applicant must meet the minimum space and investment
                requirements as set by Volmo Logistics.
              </li>
              <li>
                The franchisee is responsible for obtaining all necessary local
                business permits and legal clearances.
              </li>
              <li>
                The franchisee must operate under Volmo Logistics' branding,
                policies, and operational guidelines.
              </li>
              <li>
                Any false or misleading information provided in this form may
                lead to disqualification.
              </li>
              <li>
                The franchisee must maintain a minimum monthly operational
                standard as per company requirements.
              </li>
              <li>
                Volmo Logistics reserves the right to terminate the franchise
                agreement if performance benchmarks are not met.
              </li>
              <li>
                The franchisee must not engage in any competing business that
                directly affects Volmo Logistics' operations.
              </li>
              <li>
                The investment amount is refundable, except for the registration
                fee of ₹18,600.
              </li>
              <li>
                The security deposit earns 7.5% annual interest, and 90% of it
                is refundable after the agreement period.
              </li>
              <li>
                Any legal disputes will be resolved under the jurisdiction of
                Bangalore, Karnataka.
              </li>
            </ol>
          </div>
          <div class="mt-4">
            <label class="inline-flex items-center"
              ><input
                type="checkbox"
                id="agreeTerms"
                name="agreeTerms"
                required
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
              /><span class="ml-2 text-gray-700"
                >I agree to the terms and conditions*</span
              ></label
            >
          </div>
        </div>
        <div class="bg-gray-50 p-6 rounded-lg mb-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">Disclaimer</h3>
          <div
            class="bg-white p-4 rounded-lg shadow-sm max-h-64 overflow-y-auto"
          >
            <ul class="list-disc pl-5 space-y-3 text-sm text-gray-700">
              <li>
                Submission of this application does not guarantee approval of
                the franchise.
              </li>
              <li>
                Volmo Logistics reserves the right to verify the information
                provided and conduct background checks.
              </li>
              <li>
                The company is not liable for any investment made before
                official franchise approval.
              </li>
              <li>
                The one-time setup fee, agreement fee, and security deposit are
                fully refundable, except for the registration fee.
              </li>
              <li>The security deposit earns an annual interest of 7.5%.</li>
              <li>
                Any changes to policies, investment requirements, or operational
                guidelines will be communicated in writing.
              </li>
              <li>
                Any legal disputes or disagreements will be subject to the
                jurisdiction of Bangalore, Karnataka.
              </li>
              <li>
                This form and its contents remain the property of Volmo
                Logistics and must not be copied or shared without permission.
              </li>
            </ul>
          </div>
          <div class="mt-4">
            <label class="inline-flex items-center"
              ><input
                type="checkbox"
                id="agreeDisclaimer"
                name="agreeDisclaimer"
                required
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
              /><span class="ml-2 text-gray-700"
                >I have read and understood the disclaimer*</span
              ></label
            >
          </div>
        </div>

        <!-- Final Submit Button -->
        <div class="flex justify-end mt-8">
          <button
            type="button"
            id="submitBtn"
            class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition flex items-center"
          >
            <i class="fas fa-paper-plane mr-2"></i> Submit Application
          </button>
        </div>
      </form>
    </main>

    <footer class="bg-gray-800 text-white py-6">
      <div class="container mx-auto px-4 text-center">
        <div class="flex justify-center space-x-6 mb-4">
          <a href="#" class="text-gray-300 hover:text-white"
            ><i class="fab fa-facebook-f"></i
          ></a>
          <a href="#" class="text-gray-300 hover:text-white"
            ><i class="fab fa-twitter"></i
          ></a>
          <a href="#" class="text-gray-300 hover:text-white"
            ><i class="fab fa-linkedin-in"></i
          ></a>
          <a href="#" class="text-gray-300 hover:text-white"
            ><i class="fab fa-instagram"></i
          ></a>
        </div>
        <p class="text-sm text-gray-400">
          © 2025 Volmo Logistics. All rights reserved.
        </p>
        <p class="text-xs text-gray-500 mt-2">
          Application Number:
          <span id="applicationNumberDisplayFooter" class="font-mono"
            >VL-XXXXXX</span
          >
        </p>
      </div>
    </footer>

    <script>
      // --- CORE FUNCTIONS ---
      function generateApplicationNumber() {
        const prefix = "VL";
        const randomNum = Math.floor(100000 + Math.random() * 900000);
        const appNum = `${prefix}-${randomNum}`;
        document.getElementById("applicationNumberDisplay").textContent =
          appNum;
        document.getElementById("applicationNumberDisplayFooter").textContent =
          appNum;
        return appNum;
      }

      function showToast(message, type = "success") {
        const toast = document.getElementById("toast");
        toast.textContent = message;
        toast.className = "toast";
        toast.classList.add(type, "show");
        setTimeout(() => {
          toast.classList.remove("show");
        }, 5000);
      }

      // --- FORM VALIDATION AND SUBMISSION ---
      function validateForm() {
        const form = document.getElementById("franchiseForm");
        const requiredInputs = form.querySelectorAll("[required]");
        let isValid = true;
        let firstErrorField = null;

        requiredInputs.forEach((input) => {
          let fieldIsValid = true;
          // Handle radio buttons
          if (input.type === "radio") {
            const name = input.name;
            const group = form.querySelectorAll(`input[name="${name}"]`);
            if (!form.querySelector(`input[name="${name}"]:checked`)) {
              fieldIsValid = false;
              group.forEach((item) =>
                item.parentElement.classList.add("text-red-500")
              );
            } else {
              group.forEach((item) =>
                item.parentElement.classList.remove("text-red-500")
              );
            }
            // Handle checkboxes
          } else if (input.type === "checkbox") {
            if (!input.checked) {
              fieldIsValid = false;
              input.parentElement.classList.add("text-red-500");
            } else {
              input.parentElement.classList.remove("text-red-500");
            }
            // Handle other inputs (text, file, etc.)
          } else {
            if (!input.value.trim()) {
              fieldIsValid = false;
              input.classList.add("border-red-500");
            } else {
              input.classList.remove("border-red-500");
            }
          }

          if (!fieldIsValid) {
            isValid = false;
            if (!firstErrorField) {
              firstErrorField = input;
            }
          }
        });

        if (firstErrorField) {
          firstErrorField.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          firstErrorField.focus();
        }
        return isValid;
      }

      async function submitForm() {
        if (!validateForm()) {
          showToast("Please fill all required fields.", "error");
          return;
        }

        const form = document.getElementById("franchiseForm");
        const formData = new FormData(form);
        formData.append(
          "applicationNumber",
          document.getElementById("applicationNumberDisplay").textContent
        );

        const submitBtn = document.getElementById("submitBtn");
        submitBtn.disabled = true;
        submitBtn.innerHTML =
          '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';

        try {
          // --- REPLACE WITH YOUR ACTUAL API ENDPOINT ---
          const response = await fetch(
            "http://localhost:3000/api/submit-application",
            { method: "POST", body: formData }
          );
          // const result = await response.json();

          // --- SIMULATED API CALL FOR DEMO ---
          await new Promise((resolve) => setTimeout(resolve, 2000));
          const result = { success: true };

          if (result.success) {
            showToast("Application submitted successfully!", "success");
            form.reset();
            document
              .querySelectorAll('[id$="Preview"]')
              .forEach((p) => p.classList.add("hidden"));
            document.getElementById("declarationName").textContent = "";
            toggleAllDetails(false); // Hide all conditional fields
            generateApplicationNumber();
            window.scrollTo(0, 0);
          } else {
            showToast("Submission failed. Please try again.", "error");
          }
        } catch (error) {
          console.error("Error submitting form:", error);
          showToast("An error occurred. Please check the console.", "error");
        } finally {
          submitBtn.disabled = false;
          submitBtn.innerHTML =
            '<i class="fas fa-paper-plane mr-2"></i> Submit Application';
        }
      }

      // --- DYNAMIC FIELD VISIBILITY ---
      function toggleDetail(show, containerId, requiredInputIds) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.classList.toggle("hidden", !show);
        requiredInputIds.forEach((id) => {
          const input = document.getElementById(id);
          if (input) input.required = show;
        });
      }
      function toggleAllDetails(show) {
        toggleDetail(show, "loanDetailsContainer", ["loanDetails"]);
        toggleDetail(show, "vehicleDetailsContainer", ["vehicleDetails"]);
        toggleDetail(show, "experienceDetailsContainer", ["experienceDetails"]);
        toggleDetail(show, "franchiseDetailsContainer", ["franchiseDetails"]);
        toggleDetail(show, "legalDetailsContainer", ["legalDetails"]);
      }

      // --- FILE UPLOAD LOGIC ---
      function setupFileUploads() {
        const fileInputs = [
          { id: "panCard" },
          { id: "aadharCard" },
          { id: "passport" },
          { id: "gstCertificate" },
          { id: "businessProof" },
          { id: "cancelledCheque" },
        ];
        fileInputs.forEach((file) => {
          const inputEl = document.getElementById(file.id);
          const uploadDiv = document.getElementById(`${file.id}Upload`);
          if (!inputEl || !uploadDiv) return;

          ["dragenter", "dragover", "dragleave", "drop"].forEach((evt) => {
            uploadDiv.addEventListener(
              evt,
              (e) => {
                e.preventDefault();
                e.stopPropagation();
              },
              false
            );
          });
          ["dragenter", "dragover"].forEach((evt) => {
            uploadDiv.addEventListener(
              evt,
              () => uploadDiv.classList.add("dragover"),
              false
            );
          });
          ["dragleave", "drop"].forEach((evt) => {
            uploadDiv.addEventListener(
              evt,
              () => uploadDiv.classList.remove("dragover"),
              false
            );
          });

          uploadDiv.addEventListener(
            "drop",
            (e) => {
              inputEl.files = e.dataTransfer.files;
              handleFileSelect({ target: inputEl });
            },
            false
          );

          inputEl.addEventListener("change", (e) => handleFileSelect(e), false);
        });
      }
      function handleFileSelect(e) {
        const input = e.target;
        const file = input.files[0];
        const previewDiv = document.getElementById(`${input.id}Preview`);
        const fileNameSpan = document.getElementById(`${input.id}FileName`);
        if (!previewDiv || !fileNameSpan) return;

        if (file) {
          if (file.size > 5 * 1024 * 1024) {
            // 5MB limit
            showToast("File size must be under 5MB.", "error");
            input.value = ""; // Clear the invalid file
            return;
          }
          fileNameSpan.textContent = file.name;
          previewDiv.classList.remove("hidden");
        }
      }
      function removeFile(inputId) {
        document.getElementById(inputId).value = "";
        document.getElementById(`${inputId}Preview`).classList.add("hidden");
      }

      // --- INITIALIZATION ---
      document.addEventListener("DOMContentLoaded", function () {
        // Generate initial application number
        generateApplicationNumber();

        // Setup file uploads
        setupFileUploads();

        // Link full name to declaration
        document
          .getElementById("fullName")
          .addEventListener("input", function () {
            document.getElementById("declarationName").textContent =
              this.value || "...";
          });

        // Main submit button listener
        document
          .getElementById("submitBtn")
          .addEventListener("click", submitForm);

        // Add listeners for conditional fields
        document
          .querySelectorAll('input[name="hasLoans"]')
          .forEach((el) =>
            el.addEventListener("change", (e) =>
              toggleDetail(e.target.value === "Yes", "loanDetailsContainer", [
                "loanDetails",
              ])
            )
          );
        document
          .querySelectorAll('input[name="hasCommercialVehicles"]')
          .forEach((el) =>
            el.addEventListener("change", (e) =>
              toggleDetail(
                e.target.value === "Yes",
                "vehicleDetailsContainer",
                ["vehicleDetails"]
              )
            )
          );
        document
          .querySelectorAll('input[name="hasLogisticsExperience"]')
          .forEach((el) =>
            el.addEventListener("change", (e) =>
              toggleDetail(
                e.target.value === "Yes",
                "experienceDetailsContainer",
                ["experienceDetails"]
              )
            )
          );
        document
          .querySelectorAll('input[name="hasOtherFranchise"]')
          .forEach((el) =>
            el.addEventListener("change", (e) =>
              toggleDetail(
                e.target.value === "Yes",
                "franchiseDetailsContainer",
                ["franchiseDetails"]
              )
            )
          );
        document
          .querySelectorAll('input[name="hasLegalIssues"]')
          .forEach((el) =>
            el.addEventListener("change", (e) =>
              toggleDetail(e.target.value === "Yes", "legalDetailsContainer", [
                "legalDetails",
              ])
            )
          );
      });
    </script>
  </body>
</html>
