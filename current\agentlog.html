<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Franchise Application Form</title>
<link rel="shortcut icon" href="https://play-lh.googleusercontent.com/5NoxwjDfgEN36Jjzi-VdwT_xfNgLylDom4nWx7bv60s7yC4e-pgkl32_vbwjaBHS-A" type="image/x-icon">
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8fafc;
        }
        .form-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .form-section {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        .section-header {
            background-color: #3b82f6;
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .section-body {
            padding: 1.5rem;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        .form-field {
            margin-bottom: 1rem;
        }
        .form-label {
            display: block;
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }
        .form-value {
            font-size: 1rem;
            color: #1e293b;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
            min-height: 1.5rem;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: inline-block;
        }
        .status-Pending { background-color: #fef3c7; color: #d97706; }
        .status-Under-Review { background-color: #dbeafe; color: #2563eb; }
        .status-Approved { background-color: #d1fae5; color: #059669; }
        .status-Rejected { background-color: #fee2e2; color: #dc2626; }
        .file-link {
            color: #3b82f6;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
        }
        .file-link:hover {
            text-decoration: underline;
        }
        .boolean-yes { color: #10b981; }
        .boolean-no { color: #ef4444; }
        .application-header {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .application-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
        }
        .application-number {
            color: #64748b;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <!-- Application Header -->
        <div class="application-header">
            <div>
                <h1 class="application-title" style="t"> Franchise Application</h1>
                <p class="application-number">Application <span id="applicationNumberHeader">#VL-000000</span></p>
            </div>
            <div id="status"  style="background-color: #10b981 ;  color: #d1fae5;" class="status-badge status-Pending">Pay Now</div>
        </div>

 


<div class="form-section">
  <div class="section-header">
    <i class="fas fa-history"></i>
    <span>Application Timeline</span>
  </div>
  <div class="section-body">
    <div class="flex gap-8"> <!-- Changed from space-y-4 to flex and added gap -->
      <div class="flex items-start gap-3">
        <div class="flex flex-col items-center">
          <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center ring-4 ring-blue-50">
            <i class="fas fa-check text-blue-500 text-xs"></i>
          </div>
          <div class="w-px h-12 bg-gray-200 mt-1"></div>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-800">Application Submitted</p>
          <p class="text-xs text-gray-500" id="dateOfApplication">Loading...</p>
        </div>
      </div>

      <div class="flex items-start gap-3" id="reviewContainer">
        <div class="flex flex-col items-center">
          <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
            <i class="fas fa-clock text-gray-400 text-xs"></i>
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-600" id="reviewStatus">Under Review</p>
          <p class="text-xs text-gray-500" id="reviewDate">Awaiting review</p>
        </div>
      </div>
    </div>
  </div>
</div>






        <!-- Personal & Contact Information -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-user-tie"></i>
                <span>Personal & Contact Information</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Full Name</label>
                        <div class="form-value" id="fullName"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Father/Husband Name</label>
                        <div class="form-value" id="fatherHusbandName"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Date of Birth</label>
                        <div class="form-value" id="dateOfBirth"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Gender</label>
                        <div class="form-value" id="gender"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Nationality</label>
                        <div class="form-value" id="nationality"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Marital Status</label>
                        <div class="form-value" id="maritalStatus"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">PAN Number</label>
                        <div class="form-value" id="panNumber"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Aadhar Number</label>
                        <div class="form-value" id="aadharNumber"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Passport Number</label>
                        <div class="form-value" id="passportNumber"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Mobile Number</label>
                        <div class="form-value" id="mobileNumber"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Alternate Mobile</label>
                        <div class="form-value" id="alternateMobileNumber"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Email</label>
                        <div class="form-value" id="email"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Preferred Communication</label>
                        <div class="form-value" id="preferredCommunication"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-briefcase"></i>
                <span>Business Information</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Business Name</label>
                        <div class="form-value" id="businessName"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Business Type</label>
                        <div class="form-value" id="businessType"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">GST Number</label>
                        <div class="form-value" id="gstNumber"></div>
                    </div>
                </div>
                
                <h4 class="text-lg font-semibold text-gray-800 mt-6 mb-4">Office Address</h4>
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Street/Area</label>
                        <div class="form-value" id="officeAddress"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">City</label>
                        <div class="form-value" id="officeCity"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">District</label>
                        <div class="form-value" id="officeDistrict"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">State</label>
                        <div class="form-value" id="officeState"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Pin Code</label>
                        <div class="form-value" id="officePinCode"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-piggy-bank"></i>
                <span>Financial Information</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Investment Capacity</label>
                        <div class="form-value" id="investmentCapacity"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Source of Investment</label>
                        <div class="form-value" id="investmentSource"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Expected Monthly Revenue</label>
                        <div class="form-value" id="expectedRevenue"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Any Existing Loans?</label>
                        <div class="form-value" id="hasLoans"></div>
                    </div>
                    <div class="form-field hidden" id="loanDetailsContainer">
                        <label class="form-label">Loan Details</label>
                        <div class="form-value" id="loanDetails"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Qualifications & Experience -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-graduation-cap"></i>
                <span>Qualifications & Experience</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Education</label>
                        <div class="form-value" id="education"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Has Legal Issues?</label>
                        <div class="form-value" id="hasLegalIssues"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Professional Background</label>
                        <div class="form-value" id="professionalBackground"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Certifications</label>
                        <div class="form-value" id="certifications"></div>
                    </div>
                    <div class="form-field hidden" id="legalDetailsContainer">
                        <label class="form-label">Legal Details</label>
                        <div class="form-value" id="legalDetails"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Details -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-university"></i>
                <span>Bank Details</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Bank Name</label>
                        <div class="form-value" id="bankName"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Branch</label>
                        <div class="form-value" id="bankBranch"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Account Holder</label>
                        <div class="form-value" id="accountHolderName"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Account Number</label>
                        <div class="form-value" id="accountNumber"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">IFSC Code</label>
                        <div class="form-value" id="ifscCode"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">UPI ID</label>
                        <div class="form-value" id="upiId"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Operational Readiness -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-cogs"></i>
                <span>Operational Readiness</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">Franchise Pin Code</label>
                        <div class="form-value" id="franchisePinCode"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Premises Ownership</label>
                        <div class="form-value" id="premisesOwnership"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Total Space (sq ft)</label>
                        <div class="form-value" id="totalSpace"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Warehouse Space (sq ft)</label>
                        <div class="form-value" id="warehouseSpace"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Parking Facility</label>
                        <div class="form-value" id="parkingFacility"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Office Setup</label>
                        <div class="form-value" id="officeSetup"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Number of Employees</label>
                        <div class="form-value" id="numberOfEmployees"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Total Staff Count</label>
                        <div class="form-value" id="staffCount"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Has Commercial Vehicles?</label>
                        <div class="form-value" id="hasCommercialVehicles"></div>
                    </div>
                    <div class="form-field hidden" id="vehicleDetailsContainer">
                        <label class="form-label">Vehicle Details</label>
                        <div class="form-value" id="vehicleDetails"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Familiar with Logistics?</label>
                        <div class="form-value" id="isFamiliarWithLogistics"></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Has Logistics Experience?</label>
                        <div class="form-value" id="hasLogisticsExperience"></div>
                    </div>
                    <div class="form-field hidden" id="experienceDetailsContainer">
                        <label class="form-label">Experience Details</label>
                        <div class="form-value" id="experienceDetails"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documents -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-folder-open"></i>
                <span>Documents</span>
            </div>
            <div class="section-body">
                <div class="form-grid">
                    <div class="form-field">
                        <label class="form-label">PAN Card</label>
                        <div class="form-value"><a href="#" target="_blank" class="file-link" id="panCardPath"><i class="fas fa-external-link-alt"></i> View Document</a></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Aadhar Card</label>
                        <div class="form-value"><a href="#" target="_blank" class="file-link" id="aadharCardPath"><i class="fas fa-external-link-alt"></i> View Document</a></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Passport</label>
                        <div class="form-value"><a href="#" target="_blank" class="file-link" id="passportPath"><i class="fas fa-external-link-alt"></i> View Document</a></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">GST Certificate</label>
                        <div class="form-value"><a href="#" target="_blank" class="file-link" id="gstCertificatePath"><i class="fas fa-external-link-alt"></i> View Document</a></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Business Proof</label>
                        <div class="form-value"><a href="#" target="_blank" class="file-link" id="businessProofPath"><i class="fas fa-external-link-alt"></i> View Document</a></div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Cancelled Cheque</label>
                        <div class="form-value"><a href="#" target="_blank" class="file-link" id="cancelledChequePath"><i class="fas fa-external-link-alt"></i> View Document</a></div>
                    </div>
                </div>
            </div>
        </div>
 
      
    </div>

    <script>
        // Use a placeholder if localStorage is empty
        const applicationId = localStorage.getItem('token') || 'YOUR_APPLICATION_ID_HERE'; 
        const backendURL = 'http://localhost:3000';

        // Helper function to set text content or a default placeholder
        function setText(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value || 'N/A';
            }
        }
        
        // Helper function to format boolean values
        function formatBoolean(id, value) {
            const element = document.getElementById(id);
            if (element) {
                if (value === true) {
                    element.innerHTML = `<i class="fas fa-check-circle boolean-yes"></i> Yes`;
                } else if (value === false) {
                     element.innerHTML = `<i class="fas fa-times-circle boolean-no"></i> No`;
                } else {
                     element.textContent = 'N/A';
                }
            }
        }

        // Helper function to manage visibility of conditional fields
        function showIfTrue(value, containerId) {
             const container = document.getElementById(containerId);
             if (container) {
                container.classList.toggle('hidden', !value);
             }
        }
        
        // Helper to format currency
        function formatCurrency(value) {
            if (!value || isNaN(value)) return 'N/A';
            return `₹${Number(value).toLocaleString('en-IN')}`;
        }
        
        // Helper to set link href
        function setLink(id, path) {
            const element = document.getElementById(id);
            if (element) {
                if (path) {
                    element.href = `${backendURL}/${path}`;
                    element.style.display = 'inline-flex';
                } else {
                    element.style.display = 'none';
                }
            }
        }

        async function fetchApplicationData() {
            if (applicationId === 'YOUR_APPLICATION_ID_HERE') {
                console.error("Application ID not found in localStorage.");
                // You could populate with dummy data or show an error message
                return;
            }
            try {
                const response = await fetch(`${backendURL}/api/applications/${applicationId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                populateData(data);
            } catch (error) {
                console.error('Error fetching application data:', error);
                document.body.innerHTML = `<div class="text-center p-8"><h1>Error</h1><p>Could not load application data. Please try again later.</p></div>`;
            }
        }

        function populateData(data) {
            // Header
            setText('applicationNumberHeader', data.applicationNumber);
            const statusElement = document.getElementById('status');
            statusElement.textContent = 'Pay Now';
            statusElement.className = `status-badge status-${data.status}`;

            // Personal & Contact
            setText('fullName', data.fullName);
            setText('fatherHusbandName', data.fatherHusbandName);
            setText('dateOfBirth', formatDate(data.dateOfBirth));
            setText('gender', data.gender);
            setText('nationality', data.nationality);
            setText('maritalStatus', data.maritalStatus);
            setText('panNumber', data.panNumber);
            setText('aadharNumber', data.aadharNumber);
            setText('passportNumber', data.passportNumber);
            setText('mobileNumber', data.mobileNumber);
            setText('alternateMobileNumber', data.alternateMobileNumber);
            setText('email', data.email);
            setText('preferredCommunication', data.preferredCommunication);

            // Business & Office
            setText('businessName', data.businessName);
            setText('businessType', data.businessType);
            setText('gstNumber', data.gstNumber);
            setText('officeAddress', data.officeAddress);
            setText('officeCity', data.officeCity);
            setText('officeDistrict', data.officeDistrict);
            setText('officeState', data.officeState);
            setText('officePinCode', data.officePinCode);
            
            // Financial & Investment
            setText('investmentCapacity', data.investmentCapacity);
            setText('investmentSource', data.investmentSource);
            setText('expectedRevenue', formatCurrency(data.expectedRevenue));
            formatBoolean('hasLoans', data.hasLoans);
            setText('loanDetails', data.loanDetails);
            showIfTrue(data.hasLoans, 'loanDetailsContainer');

            // Qualifications & Experience
            setText('education', data.education);
            setText('professionalBackground', data.professionalBackground);
            setText('certifications', data.certifications);
            formatBoolean('hasLegalIssues', data.hasLegalIssues);
            setText('legalDetails', data.legalDetails);
            showIfTrue(data.hasLegalIssues, 'legalDetailsContainer');

            // Bank Details
            setText('bankName', data.bankName);
            setText('bankBranch', data.bankBranch);
            setText('accountHolderName', data.accountHolderName);
            setText('accountNumber', data.accountNumber);
            setText('ifscCode', data.ifscCode);
            setText('upiId', data.upiId);

            // Operational Readiness
            setText('franchisePinCode', data.franchisePinCode);
            setText('premisesOwnership', data.premisesOwnership);
            setText('totalSpace', data.totalSpace ? `${data.totalSpace} sq ft` : 'N/A');
            setText('warehouseSpace', data.warehouseSpace ? `${data.warehouseSpace} sq ft` : 'N/A');
            setText('parkingFacility', data.parkingFacility);
            setText('officeSetup', data.officeSetup);
            setText('numberOfEmployees', data.numberOfEmployees);
            setText('staffCount', data.staffCount);
            formatBoolean('hasCommercialVehicles', data.hasCommercialVehicles);
            setText('vehicleDetails', data.vehicleDetails);
            showIfTrue(data.hasCommercialVehicles, 'vehicleDetailsContainer');
            formatBoolean('isFamiliarWithLogistics', data.isFamiliarWithLogistics);
            formatBoolean('hasLogisticsExperience', data.hasLogisticsExperience);
            setText('experienceDetails', data.experienceDetails);
            showIfTrue(data.hasLogisticsExperience, 'experienceDetailsContainer');

            // Documents
            setLink('panCardPath', data.panCardPath);
            setLink('aadharCardPath', data.aadharCardPath);
            setLink('passportPath', data.passportPath);
            setLink('gstCertificatePath', data.gstCertificatePath);
            setLink('businessProofPath', data.businessProofPath);
            setLink('cancelledChequePath', data.cancelledChequePath);

            // Timeline
            setText('dateOfApplication', formatDateTime(data.dateOfApplication));
            if(data.reviewDate) {
                setText('reviewDate', formatDateTime(data.reviewDate));
                setText('reviewStatus', data.status.replace('-', ' '));
                 document.querySelector('#reviewContainer .fa-clock').className = `fas fa-check text-blue-500 text-xs`;
            }
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const options = { day: 'numeric', month: 'short', year: 'numeric' };
            return new Date(dateString).toLocaleDateString('en-GB', options);
        }

        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            const options = { day: 'numeric', month: 'short', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: true };
            return date.toLocaleDateString('en-GB', options).replace(',', '');
        }

        document.addEventListener('DOMContentLoaded', fetchApplicationData);
    </script>
</body>
</html>