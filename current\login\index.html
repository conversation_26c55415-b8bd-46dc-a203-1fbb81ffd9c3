<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Customer Login</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"/>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">

  <!-- HEADER -->
  <header class="bg-blue-600 shadow-md py-4 px-6 flex items-center justify-between text-white">
    <h1 class="text-2xl font-bold"><img src="https://registrations-meesho-valmo.in/valmologo.png" alt=""></h1>
     
  </header>

  <!-- MAIN CONTENT -->
  <main class="flex flex-1 items-center justify-center">
    <div class="flex w-full max-w-4xl bg-white shadow-lg rounded-lg overflow-hidden">

      <!-- LEFT SIDE IMAGE (Hidden on mobile) -->
      <div class="hidden md:flex w-1/2 items-center justify-center bg-gray-50">
        <img src="https://registrations-meesho-valmo.in/truck_image_desktop.png"
             alt="Login Illustration" 
             class="max-w-[80%] max-h-[80%] object-contain">
      </div>

      <!-- RIGHT SIDE FORM -->
      <div class="w-full md:w-1/2 p-8 flex flex-col justify-center bg-gradient-to-b from-gray-50 to-white">
        <h2 class="text-2xl font-bold text-center text-blue-600 mb-2">Customer Login</h2>
        <p class="text-gray-500 text-center mb-6">Please enter your credentials</p>

        <!-- Alert messages -->
        <div id="messageBox" class="hidden text-center py-2 mb-4 rounded"></div>

        <form id="loginForm" class="space-y-4">
          <div>
            <label for="userId" class="block mb-1 font-medium">User ID</label>
            <input type="text" id="userId" name="userId" 
                   class="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-black"
                   placeholder="Enter your User ID">
          </div>
          <div>
            <label for="password" class="block mb-1 font-medium">Password</label>
            <input type="password" id="password" name="password" 
                   class="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-black"
                   placeholder="Enter your Password">
          </div>

          <button type="submit" id="loginBtn" 
                  class="w-full bg-black text-white py-2 rounded hover:bg-gray-800 transition">
            Login
          </button>
        </form>
      </div>
    </div>
  </main>

  <!-- FOOTER -->
  <footer class="bg-gray-900 text-center text-gray-300 text-sm py-3">
    © 2025 Valmo. All rights reserved.
  </footer>
<script>
  const form = document.getElementById("loginForm");
  const messageBox = document.getElementById("messageBox");
  const loginBtn = document.getElementById("loginBtn");

  function showMessage(text, type) {
    messageBox.textContent = text;
    messageBox.classList.remove("hidden", "bg-red-100", "text-red-600", "bg-green-100", "text-green-600");
    if (type === "error") {
      messageBox.classList.add("bg-red-100", "text-red-600");
    } else {
      messageBox.classList.add("bg-green-100", "text-green-600");
    }
  }

  form.addEventListener("submit", async (e) => {
    e.preventDefault();

    const userId = document.getElementById("userId").value.trim();
    const password = document.getElementById("password").value.trim();

    if (!userId || !password) {
      showMessage("Please fill in all fields", "error");
      return;
    }

    loginBtn.disabled = true;
    loginBtn.textContent = "Logging in...";

    try {
      // Call your backend API
      const res = await fetch("http://localhost:3000/customer-login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, password }),
      });

      const result = await res.json();

      if (result.success) {
        showMessage("Login successful! Redirecting...", "success");
        // Save token (if backend sends one)
        if (result.token) {
          localStorage.setItem("token", result.token);
        }
        setTimeout(() => {
          window.location.href = "/agentlog.html";
        }, 1500);
      } else {
        showMessage(result.message || "Invalid User ID or Password", "error");
      }
    } catch (error) {
      console.error("Login error:", error);
      showMessage("Something went wrong. Please try again.", "error");
    } finally {
      loginBtn.disabled = false;
      loginBtn.textContent = "Login";
    }
  });
</script>

</body>
</html>
