 
const express = require("express");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const cors = require("cors");
const nodemailer = require("nodemailer");
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files
app.use(express.static(__dirname));

// MongoDB Connection
mongoose.connect("mongodb+srv://fsdfsdffsdfsdf:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0")
  .then(() => console.log("MongoDB Connected"))
  .catch(err => console.error("MongoDB Error:", err));

// User Model
const UserSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  userType: { type: String, enum: ["admin", "agent"], required: true },
  name: String, 
  phone: String,
  pincode: String,
  location: String,
  documents: {
    aadhar: <PERSON><PERSON><PERSON>,
    pan: <PERSON>olean,
    education: <PERSON><PERSON><PERSON>,
    photo: Boolean
  }
});

const User = mongoose.model("User", UserSchema);






async function createDummyAdminUser() {
  try {
    const dummyData = {
      email: 's',
      password: 's',
      userType: 'admin',
      name: 'Admin User',
      phone: '9999999999',
      pincode: '110001',
      location: 'Delhi',
      documents: {
        aadhar: true,
        pan: true,
        education: true,
        photo: true
      }
    };

    // Check if email already exists
    const existingUser = await User.findOne({ email: dummyData.email });
    if (existingUser) {
      console.log('Admin already exists with this email.');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(dummyData.password, 10);

    // Create new admin user
    const newAdmin = new User({
      ...dummyData,
      password: hashedPassword
    });

    await newAdmin.save();
    console.log('✅ Dummy admin user created successfully:', newAdmin);
  } catch (err) {
    console.error('❌ Error creating dummy admin user:', err);
  }
}



// createDummyAdminUser()

// Root route - serve the main page
app.get('/', (req, res) => {
  res.sendFile(__dirname + '/index.html');
});

// Login Endpoint
app.post("/api/login", async (req, res) => {
  const { email, password, userType } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) return res.status(401).json({ message: "Invalid credentials" });

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) return res.status(401).json({ message: "Invalid credentials" });

    if (user.userType !== userType) {
      return res.status(401).json({ message: `Please login as ${user.userType}` });
    }

    res.json({ success: true, userType, userId: user._id });
  } catch (err) {
    res.status(500).json({ message: "Server error" });
  }
});

// Lead/Agent Management
app.post("/api/leads", async (req, res) => {
  try {
    const hashedPassword = await bcrypt.hash(req.body.password, 10);
    const user = new User({
      ...req.body,
      password: hashedPassword,
      userType: "agent"
    });
    await user.save();
    res.status(201).json(user);
  } catch (err) {
    console.log(err)
    res.status(400).json({ message: err.message });
  }
});

app.get("/api/leads", async (req, res) => {
  try {
    const agents = await User.find({ userType: "agent" });
    res.json(agents);
  } catch (err) {
    
    res.status(500).json({ message: err.message });
  }
});


app.get("/api/agent/:id", async (req, res) => {
  try {
    const agent = await User.findById(req.params.id);
    if (!agent) return res.status(404).json({ message: "Agent not found" });
    
  

    res.json({ agent });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

 









const multer = require('multer');
const path = require('path');
 
const fs = require('fs');

 

 
app.use(express.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

 

// File Upload Configuration
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = 'uploads/';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Only JPEG, PNG, and PDF are allowed.'), false);
    }
};

const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Franchise Application Schema
const franchiseApplicationSchema = new mongoose.Schema({
    applicationNumber: { type: String, required: true, unique: true },
    dateOfApplication: { type: Date, default: Date.now },
    
    // Personal Information
    fullName: { type: String, required: true },
    fatherHusbandName: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    gender: { type: String, required: true, enum: ['Male', 'Female', 'Other'] },
    nationality: { type: String, required: true },
    maritalStatus: { type: String, required: true, enum: ['Single', 'Married', 'Other'] },
    panNumber: { type: String, required: true },
    aadharNumber: { type: String, required: true },
    passportNumber: { type: String },
    
    // Contact Details
    mobileNumber: { type: String, required: true },
    alternateMobileNumber: { type: String },
    email: { type: String, required: true },
    preferredCommunication: { type: String, required: true, enum: ['Phone', 'Email', 'WhatsApp'] },
    
    // Residential Address
    residentialStreet: { type: String, required: true },
    residentialCity: { type: String, required: true },
    residentialDistrict: { type: String, required: true },
    residentialState: { type: String, required: true },
    residentialPinCode: { type: String, required: true },
    
    // Business Information
    businessName: { type: String },
    businessType: { type: String, required: true, enum: ['Sole Proprietor', 'Partnership', 'Private Ltd', 'LLP', 'Other'] },
    gstNumber: { type: String },
    officeAddress: { type: String, required: true },
    officeCity: { type: String, required: true },
    officeDistrict: { type: String, required: true },
    officeState: { type: String, required: true },
    officePinCode: { type: String, required: true },
    numberOfEmployees: { type: Number },
    
    // Franchise Location Details
    franchisePinCode: { type: String, required: true },
    premisesOwnership: { type: String, required: true, enum: ['Own', 'Rent'] },
    totalSpace: { type: Number, required: true },
    warehouseSpace: { type: Number },
    parkingFacility: { type: String, required: true, enum: ['Yes', 'No'] },
    officeSetup: { type: String, required: true, enum: ['Fully Furnished', 'Partially Furnished', 'Unfurnished'] },
    
    // Investment & Financial Information
    investmentCapacity: { type: String, required: true, enum: ['Below ₹5 Lakhs', '₹5-10 Lakhs', '₹10-20 Lakhs', 'Above ₹20 Lakhs'] },
    investmentSource: { type: String, required: true, enum: ['Self-Funded', 'Loan', 'Business Partner', 'Other'] },
    hasLoans: { type: Boolean, required: true },
    loanDetails: { type: String },
    expectedRevenue: { type: Number, required: true },
    
    // Logistics & Operational Readiness
    hasCommercialVehicles: { type: Boolean, required: true },
    vehicleDetails: { type: String },
    isFamiliarWithLogistics: { type: Boolean, required: true },
    hasLogisticsExperience: { type: Boolean, required: true },
    experienceDetails: { type: String },
    staffCount: { type: Number, required: true },
    
    // Qualification Details
    education: { type: String, required: true, enum: ['10th Pass', '12th Pass', 'Diploma', 'Graduate', 'Postgraduate', 'Other'] },
    professionalBackground: { type: String },
    certifications: { type: String },
    
    // Past Business Experience & References
    hasOtherFranchise: { type: Boolean, required: true },
    franchiseDetails: { type: String },
    hasLegalIssues: { type: Boolean, required: true },
    legalDetails: { type: String },
    reference1Name: { type: String, required: true },
    reference1Contact: { type: String, required: true },
    reference1Relationship: { type: String, required: true },
    reference2Name: { type: String, required: true },
    reference2Contact: { type: String, required: true },
    reference2Relationship: { type: String, required: true },
    
    // Bank Details
    bankName: { type: String, required: true },
    bankBranch: { type: String, required: true },
    accountHolderName: { type: String, required: true },
    accountNumber: { type: String, required: true },
    ifscCode: { type: String, required: true },
    upiId: { type: String },
    
    // Document Paths
    panCardPath: { type: String },
    aadharCardPath: { type: String },
    passportPath: { type: String },
    gstCertificatePath: { type: String },
    businessProofPath: { type: String },
    cancelledChequePath: { type: String },
    
    // Agreement
    agreeTerms: { type: Boolean, required: true },
    agreeDisclaimer: { type: Boolean, required: true },
    
    // Status
    status: { type: String, default: 'Pending', enum: ['Pending', 'Under Review', 'Approved', 'Rejected'] },
    remarks: { type: String },
    reviewedBy: { type: String },
    reviewDate: { type: Date }
});

const FranchiseApplication = mongoose.model('FranchiseApplication', franchiseApplicationSchema);

// Routes
// app.get('/api/submit-application', async (req, res) => {
//     try {
//         const applications = await FranchiseApplication.find().sort({ dateOfApplication: -1 });
//         res.json(applications);
//     } catch (err) {
//         res.status(500).json({ message: err.message });
//     }
// });




app.post('/customer-login', async (req, res) => {
    try {
        const { userId, password } = req.body;
console.log(req.body)
        const customer = await FranchiseApplication.findOne({applicationNumber:userId });
        if (!customer) {
            return res.status(400).json({ success: false, message: "Invalid User ID or Password" });
        }

        const isMatch = password==="valmo@123";
        if (!isMatch) {
            return res.status(400).json({ success: false, message: "Invalid User ID or Password" });
        }

        // Generate token
        const token =customer._id  ;

        res.json({ success: true, message: "Login successful", token });
    } catch (err) {
        res.status(500).json({ success: false, message: err.message });
    }
});














app.get('/api/applications/:id', async (req, res) => {
    try {
        const application = await FranchiseApplication.findById(req.params.id);
        if (!application) {
            return res.status(404).json({ message: 'Application not found' });
        }
        res.json(application);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
});





app.get('/api/applications/del/:id', async (req, res) => {
    try {
        const application = await FranchiseApplication.findOneAndDelete({mobileNumber:req.params.id});
        if (!application) {
            return res.status(404).json({ message: 'Application not found' });
        }
        res.json(application);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
});


app.get('/api/applications', async (req, res) => {
    try {
        const application = await FranchiseApplication.find();
       
        res.json(application);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
});

app.post('/api/submit-application', upload.fields([
    { name: 'panCard', maxCount: 1 },
    { name: 'aadharCard', maxCount: 1 },
    { name: 'passport', maxCount: 1 },
    { name: 'gstCertificate', maxCount: 1 },
    { name: 'businessProof', maxCount: 1 },
    { name: 'cancelledCheque', maxCount: 1 }
]), async (req, res) => {
    try {
        // Generate application number
        const prefix = 'VL';
        const randomNum = Math.floor(100000 + Math.random() * 900000);
        const applicationNumber = `${prefix}-${randomNum}`;
        
        // Create application object
        const applicationData = {
            applicationNumber,
            ...req.body,
            dateOfBirth: new Date(req.body.dateOfBirth),
            hasLoans: req.body.hasLoans === 'Yes',
            hasCommercialVehicles: req.body.hasCommercialVehicles === 'Yes',
            isFamiliarWithLogistics: req.body.isFamiliarWithLogistics === 'Yes',
            hasLogisticsExperience: req.body.hasLogisticsExperience === 'Yes',
            hasOtherFranchise: req.body.hasOtherFranchise === 'Yes',
            hasLegalIssues: req.body.hasLegalIssues === 'Yes',
            agreeTerms: req.body.agreeTerms === 'on',
            agreeDisclaimer: req.body.agreeDisclaimer === 'on'
        };
        
        // Add file paths if files were uploaded
        if (req.files) {
            if (req.files.panCard) applicationData.panCardPath = req.files.panCard[0].path;
            if (req.files.aadharCard) applicationData.aadharCardPath = req.files.aadharCard[0].path;
            if (req.files.passport) applicationData.passportPath = req.files.passport[0].path;
            if (req.files.gstCertificate) applicationData.gstCertificatePath = req.files.gstCertificate[0].path;
            if (req.files.businessProof) applicationData.businessProofPath = req.files.businessProof[0].path;
            if (req.files.cancelledCheque) applicationData.cancelledChequePath = req.files.cancelledCheque[0].path;
        }
        
        const application = new FranchiseApplication(applicationData);
        await application.save();
        
        res.status(201).json({
            success: true,
            applicationId: application._id,
            applicationNumber: application.applicationNumber
        });
    } catch (err) {
        console.error(err);
        res.status(400).json({ message: err.message });
    }
});

app.put('/api/applications/:id/status', async (req, res) => {
    try {
        const { status, remarks, reviewedBy } = req.body;
        
        const application = await FranchiseApplication.findByIdAndUpdate(
            req.params.id,
            {
                status,
                remarks,
                reviewedBy,
                reviewDate: new Date()
            },
            { new: true }
        );
        
        if (!application) {
            return res.status(404).json({ message: 'Application not found' });
        }
        
        res.json(application);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Something went wrong!' });
});





















app.post("/api/send-email", async (req, res) => {
  const { agentId, emailType } = req.body;
  const testAccount = await nodemailer.createTestAccount(); // creates a fake test account
  console.log(testAccount)
  const transporter = nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
    auth: {
      user: testAccount.user,
      pass: testAccount.pass,
    },
  });

  try {
    const agent = await User.findById(agentId);
 
    if (!agent) return res.status(404).json({ message: "Agent not found" });

    const templates = {
welcome: {
  subject: `🌟🚀 Exciting Franchise Opportunity with Valmo Logistics! 🚀🌟`,
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
      <div style="background-color: #0056b3; padding: 20px; text-align: center;">
        <img src="https://www.valmo.in/static-assets/valmo-web/valmo-logo-white.svg" alt="Valmo Logistics" style="max-width: 200px;">
      </div>
      
      <div style="padding: 20px;">
        <h1 style="color: #0056b3; text-align: center;">Welcome to Valmo Logistics!</h1>
        
        <p>👋 Dear ${agent.name},</p>
        
        <p>💥 Greetings from <strong>Valmo Logistics</strong>! 🚛📦<br>
        As India's leading logistics partner, we pride ourselves on delivering reliable, fast, and cost-effective shipping solutions—ensuring smooth and efficient deliveries at the lowest cost.</p>
        
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h2 style="color: #0056b3; text-align: center;">🔥 Great News!</h2>
          <p>Your preferred location and PIN code are available for a Valmo Franchise Partnership—an incredible chance to join one of India's fastest-growing logistics companies!</p>
        </div>
        
        <h3 style="color: #0056b3; border-bottom: 2px solid #0056b3; padding-bottom: 5px;">✨ Why Partner with Valmo?</h3>
        <ul style="line-height: 1.6;">
          <li>✅ 🚀 9+ lakh orders shipped daily</li>
          <li>✅ 👥 30,000+ delivery executives</li>
          <li>✅ 🤝 3,000+ partners</li>
          <li>✅ 🌐 6,000+ PIN codes served</li>
        </ul>
        
        <h3 style="color: #0056b3; border-bottom: 2px solid #0056b3; padding-bottom: 5px;">📍 Preferred Location & PIN Code Availability</h3>
        <p>🔹 PIN Code: ${agent.pincode || 'Your Pincode'}</p>
        <p>🔹 Location: ${agent.location || 'Your Location'}</p>
        
        <div style="background-color: #fff8e1; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
          <h3 style="color: #0056b3; margin-top: 0;">🔥 Franchise Opportunities & Earnings 💰</h3>
          
          <h4>💼 1. Basic Model</h4>
          <p>💸 Total Investment: ₹1,08,700</p>
          <p>🔹 ₹18,600 ➡ Registration charge for PIN code booking</p>
          <p>🔹 ₹90,100 ➡ Agreement fee (fully refundable within 90 days)</p>
          
          <h4>📦 Earnings:</h4>
          <p>💰 ₹30 per shipment (300 products/day commitment)</p>
          <p>❌ ₹7 per parcel if cancelled at your warehouse or office</p>
          <p>🚪 ₹15 per parcel if a customer cancels on the doorstep</p>
          
          <h4>🚚 2. FOCO Model (Full Company Ownership)</h4>
          <p>💸 Total Investment: ₹3,08,700</p>
          <p>🔹 ₹18,600 ➡ Registration charge for PIN code booking</p>
          <p>🔹 ₹90,100 ➡ Agreement fee (fully refundable within 90 days)</p>
          <p>🔹 ₹2,00,000 ➡ Security deposit (refundable when you exit the franchise)</p>
          
          <h4>📦 Earnings:</h4>
          <p>💰 ₹30 per shipment (300 products/day commitment)</p>
          <p>❌ ₹7 per parcel if cancelled at your warehouse or office</p>
          <p>🚪 ₹15 per parcel if a customer cancels on the doorstep</p>
          
          <h4>⭐ Additional Benefits in FOCO Model:</h4>
          <p>👩‍💼 3 employees provided by Valmo (salaries covered by the company, approx. ₹15,000/month per employee)</p>
          <p>🏢 50% rent & electricity bill covered by the company</p>
          <p>💻 Office setup with company-designed interiors</p>
          <p>🖥 All necessary equipment provided (barcode machine + 3 laptops with accessories)</p>
        </div>
        
        <h3 style="color: #0056b3; border-bottom: 2px solid #0056b3; padding-bottom: 5px;">📑 Required Documents:</h3>
        <p>${agent.documents?.aadhar ? '✅' : '🪪'} Aadhar Card / Voter ID</p>
        <p>${agent.documents?.pan ? '✅' : '🛡'} PAN Card</p>
        <p>${agent.documents?.education ? '✅' : '📄'} Education Certificate</p>
        <p>${agent.documents?.photo ? '✅' : '📸'} Passport-size Photograph</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="http://127.0.0.1:5500/form.html?user=${agent._id}" 
             style="background-color: #0056b3; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
            Complete Your Registration Now
          </a>
        </div>
        
        <h3 style="color: #0056b3; border-bottom: 2px solid #0056b3; padding-bottom: 5px;">📲 For More Details, Contact Us:</h3>
        <p>📞 9654230611</p>
        <p>📧 <EMAIL></p>
        <p>🌐 www.valmodelivery.com</p>
        
        <p style="font-style: italic;">📍 Office Address:<br>
        🏢 3rd Floor, Wing-E, Helios Business Park, Kadubeesanahalli Village, Varthur Hobli, Outer Ring Road, Bellandur, Bangalore South, Karnataka, India, 560103</p>
        
        <p>🚀 We look forward to a successful partnership with you and are excited to grow together!</p>
        
        <p style="margin-top: 30px;">✨ Best Regards,<br>
        🤝 ${agent.name}<br>
        💼 Business Development Team<br>
        🚛 Valmo Logistics</p>
      </div>
      
      <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px;">
        <p>© ${new Date().getFullYear()} Valmo Logistics. All rights reserved.</p>
        <p><a href="#" style="color: #0056b3;">Privacy Policy</a> | <a href="#" style="color: #0056b3;">Terms of Service</a></p>
      </div>
    </div>
  `
}

,








      agreement: {
        subject: `Agreement Details - ${agent.name}`,
        html: `Dear ${agent.name}, here are your agreement details...`
      },
      approval: {
        subject: `Approval Confirmation - ${agent.name}`,
        html: `Congratulations ${agent.name}! Your application has been approved...`
      }
    };

 const testAccount=   await transporter.sendMail({
      from: '"Valmo Logistics" <<EMAIL>>',
      to: agent.email,
      ...templates[emailType]
    }); 
console.log('Preview URL: ' + nodemailer.getTestMessageUrl(testAccount));
    res.json({ message: "Email sent successfully" });
  } catch (err) {
    console.log(err)
    res.status(500).json({ message: "Failed to send email" });
  }
});

// Start server
const PORT = 3000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));





















 


 
















