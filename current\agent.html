<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Profile</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .document-badge {
      transition: all 0.2s ease;
    }
    .document-badge:hover {
      transform: scale(1.05);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
  
  <!-- Profile Card (Only UserSchema Data) -->
  <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl overflow-hidden">
    <!-- Header -->
    <div class="bg-blue-600 text-white p-6 text-center">
      <div class="w-24 h-24 mx-auto rounded-full bg-white text-blue-600 flex items-center justify-center text-4xl font-bold mb-4 shadow-md">
        <i class="fas fa-user"></i>
      </div>
      <h1 class="text-2xl font-bold" id="agentName">Loading...</h1>
      <p class="text-blue-100" id="agentLocation">Location: Loading...</p>
      <p class="text-blue-100" id="agentPincode">PIN: Loading...</p>
    </div>

    <!-- Body -->
    <div class="p-6">
      <!-- Basic Info -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <i class="fas fa-envelope text-blue-600"></i>
          <div>
            <p class="text-gray-500 text-sm">Email</p>
            <p class="font-medium" id="profileEmail">Loading...</p>
          </div>
        </div>

        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <i class="fas fa-phone-alt text-green-600"></i>
          <div>
            <p class="text-gray-500 text-sm">Phone</p>
            <p class="font-medium" id="profilePhone">Loading...</p>
          </div>
        </div>

        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <i class="fas fa-user-tag text-purple-600"></i>
          <div>
            <p class="text-gray-500 text-sm">Role</p>
            <p class="font-medium" id="userType">Loading...</p>
          </div>
        </div>
      </div>

      <!-- Document Verification -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-3 border-b pb-2">Document Verification</h3>
        <div class="grid grid-cols-2 gap-3">
          <div id="aadharVerified" class="document-badge flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <i class="fas fa-id-card text-blue-600"></i>
            <span>Aadhar Card</span>
          </div>
          <div id="panVerified" class="document-badge flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <i class="fas fa-file-alt text-yellow-600"></i>
            <span>PAN Card</span>
          </div>
          <div id="educationVerified" class="document-badge flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <i class="fas fa-graduation-cap text-green-600"></i>
            <span>Education Proof</span>
          </div>
          <div id="photoVerified" class="document-badge flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <i class="fas fa-camera text-red-600"></i>
            <span>Photo</span>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <button onclick="logout()" class="w-full py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center justify-center space-x-2">
        <i class="fas fa-sign-out-alt"></i>
        <span>Logout</span>
      </button>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', async () => {
      if (!localStorage.getItem('userId')) {
        window.location.href = 'login.html';
        return;
      }

      const agentId = new URLSearchParams(window.location.search).get('id') || localStorage.getItem('userId');
      
      try {
        const response = await fetch(`http://localhost:3000/api/agent/${agentId}`);
        const data = await response.json();
        
        if (!response.ok) throw new Error(data.message || 'Failed to load data');

        // Update Profile Data
        document.getElementById('agentName').textContent = data.agent.name || 'Not Provided';
        document.getElementById('agentPincode').textContent = `PIN: ${data.agent.pincode || 'N/A'}`;
        document.getElementById('agentLocation').textContent = `Location: ${data.agent.location || 'N/A'}`;
        document.getElementById('profileEmail').textContent = data.agent.email || 'N/A';
        document.getElementById('profilePhone').textContent = data.agent.phone || 'N/A';
        document.getElementById('userType').textContent = data.agent.userType === 'admin' ? 'Admin' : 'Agent';

        // Update Document Verification Status
        updateDocumentBadge('aadharVerified', data.agent.documents?.aadhar);
        updateDocumentBadge('panVerified', data.agent.documents?.pan);
        updateDocumentBadge('educationVerified', data.agent.documents?.education);
        updateDocumentBadge('photoVerified', data.agent.documents?.photo);

      } catch (err) {
        console.error('Error loading agent data:', err);
        alert('Failed to load profile. Please try again.');
      }
    });

    function updateDocumentBadge(id, isVerified) {
      const badge = document.getElementById(id);
      if (isVerified) {
        badge.innerHTML += '<span class="ml-auto text-green-600"><i class="fas fa-check-circle"></i></span>';
      } else {
        badge.innerHTML += '<span class="ml-auto text-red-600"><i class="fas fa-times-circle"></i></span>';
      }
    }

    function logout() {
      localStorage.clear();
      window.location.href = '/login';
    }
  </script>
</body>
</html>