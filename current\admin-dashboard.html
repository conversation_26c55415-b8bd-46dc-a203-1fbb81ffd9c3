<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Valmo Admin Panel</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="bg-gray-100">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-center mb-8">Valmo Logistics Admin Panel</h1>
    
    <!-- Add Lead Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
      <h2 class="text-xl font-semibold mb-4">Add New Lead</h2>
      <form id="addLeadForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input type="text" id="name" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input type="email" id="email" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input type="tel" id="phone" required class="w-full p-2 border rounded">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">PIN Code</label>
          <input type="text" id="pincode" required class="w-full p-2 border rounded">
        </div>
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
          <input type="text" id="location" required class="w-full p-2 border rounded">
        </div>
        <button type="submit" class="md:col-span-2 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
          Add Lead
        </button>
      </form>
    </div>
    
    <!-- Leads Table -->
    <div class="bg-white p-6 rounded-lg shadow-md">
      <h2 class="text-xl font-semibold mb-4">All Leads</h2>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead>
            <tr>
              <th class="py-2 px-4 border-b">Name</th>
              <th class="py-2 px-4 border-b">Email</th>
              <th class="py-2 px-4 border-b">Phone</th>
              <th class="py-2 px-4 border-b">PIN Code</th>
              <th class="py-2 px-4 border-b">Location</th>
              <th class="py-2 px-4 border-b">Status</th>
              <th class="py-2 px-4 border-b">Actions</th>
            </tr>
          </thead>
          <tbody id="leadsTable">
            <!-- Leads will be populated here -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const addLeadForm = document.getElementById('addLeadForm');
      const leadsTable = document.getElementById('leadsTable');
      
      // Add new lead
      addLeadForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const lead = {
          name: document.getElementById('name').value,
          email: document.getElementById('email').value,
          phone: document.getElementById('phone').value,
          pincode: document.getElementById('pincode').value,
          location: document.getElementById('location').value
        };
        
        try {
          const response = await fetch('http://localhost:3000/api/leads', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(lead)
          });
          
          const newLead = await response.json();
          alert('Lead added successfully!');
          addLeadForm.reset();
          fetchLeads();
        } catch (err) {
          console.error('Error adding lead:', err);
          alert('Failed to add lead');
        }
      });
      
      // Fetch all leads
      async function fetchLeads() {
        try {
          const response = await fetch('http://localhost:3000/api/leads');
          const leads = await response.json();
          
          leadsTable.innerHTML = leads.map(lead => `
            <tr>
              <td class="py-2 px-4 border-b">${lead.name}</td>
              <td class="py-2 px-4 border-b">${lead.email}</td>
              <td class="py-2 px-4 border-b">${lead.phone}</td>
              <td class="py-2 px-4 border-b">${lead.pincode}</td>
              <td class="py-2 px-4 border-b">${lead.location}</td>
              <td class="py-2 px-4 border-b">${lead.status}</td>
              <td class="py-2 px-4 border-b">
                <button onclick="sendEmail('${lead._id}', 'welcome')" class="bg-green-500 text-white p-1 rounded mr-1" title="Send Welcome Email">
                  <i class="fas fa-envelope"></i>
                </button>
                <button onclick="sendEmail('${lead._id}', 'agreement')" class="bg-blue-500 text-white p-1 rounded mr-1" title="Send Agreement Email">
                  <i class="fas fa-file-signature"></i>
                </button>
                <button onclick="sendEmail('${lead._id}', 'approval')" class="bg-purple-500 text-white p-1 rounded" title="Send Approval Email">
                  <i class="fas fa-check-circle"></i>
                </button>
              </td>
            </tr>
          `).join('');
        } catch (err) {
          console.error('Error fetching leads:', err);
        }
      }
      
      // Send email
      window.sendEmail = async (leadId, emailType) => {
        try {
          const response = await fetch('http://localhost:3000/api/send-email', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ leadId, emailType })
          });
          
          const result = await response.json();
          alert(`${emailType} email sent successfully!`);
          fetchLeads();
        } catch (err) {
          console.error('Error sending email:', err);
          alert('Failed to send email');
        }
      };
      
      // Initial load
      fetchLeads();
    });
  </script>
</body>
</html>